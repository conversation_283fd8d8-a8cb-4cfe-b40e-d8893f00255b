@import "tailwindcss";

/* Custom Fonts */
@font-face {
  font-family: 'Debra';
  src: url('/assets/fonts/Debra.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Debra';
  src: url('/assets/fonts/Debra-Bold.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-dark-900 dark:text-gray-100;
    @apply min-h-screen;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700;
  }

  .card {
    @apply bg-white dark:bg-dark-800 rounded-lg shadow-md p-4;
  }

  .brand-name {
    font-family: 'Debra', system-ui, sans-serif;
    font-weight: bold;
  }
}
