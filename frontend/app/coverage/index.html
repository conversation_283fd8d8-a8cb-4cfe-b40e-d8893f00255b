
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.38% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>5250/6296</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">36.84% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>7/19</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.32% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/79</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.38% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>5250/6296</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="42" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42" class="pct low">42%</td>
	<td data-value="50" class="abs low">21/50</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="42" class="pct low">42%</td>
	<td data-value="50" class="abs low">21/50</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="98.24" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.24" class="pct high">98.24%</td>
	<td data-value="1308" class="abs high">1285/1308</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="98.24" class="pct high">98.24%</td>
	<td data-value="1308" class="abs high">1285/1308</td>
	</tr>

<tr>
	<td class="file high" data-value="src/layouts"><a href="src/layouts/index.html">src/layouts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="289" class="abs high">289/289</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="289" class="abs high">289/289</td>
	</tr>

<tr>
	<td class="file high" data-value="src/router"><a href="src/router/index.html">src/router</a></td>
	<td data-value="95.03" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.03" class="pct high">95.03%</td>
	<td data-value="141" class="abs high">134/141</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="95.03" class="pct high">95.03%</td>
	<td data-value="141" class="abs high">134/141</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="61.36" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 61%"></div><div class="cover-empty" style="width: 39%"></div></div>
	</td>
	<td data-value="61.36" class="pct medium">61.36%</td>
	<td data-value="44" class="abs medium">27/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="61.36" class="pct medium">61.36%</td>
	<td data-value="44" class="abs medium">27/44</td>
	</tr>

<tr>
	<td class="file low" data-value="src/stores"><a href="src/stores/index.html">src/stores</a></td>
	<td data-value="19.22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 19%"></div><div class="cover-empty" style="width: 81%"></div></div>
	</td>
	<td data-value="19.22" class="pct low">19.22%</td>
	<td data-value="957" class="abs low">184/957</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="7.04" class="pct low">7.04%</td>
	<td data-value="71" class="abs low">5/71</td>
	<td data-value="19.22" class="pct low">19.22%</td>
	<td data-value="957" class="abs low">184/957</td>
	</tr>

<tr>
	<td class="file high" data-value="src/views"><a href="src/views/index.html">src/views</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2673" class="abs high">2673/2673</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2673" class="abs high">2673/2673</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/views/auth"><a href="src/views/auth/index.html">src/views/auth</a></td>
	<td data-value="76.37" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.37" class="pct medium">76.37%</td>
	<td data-value="834" class="abs medium">637/834</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="76.37" class="pct medium">76.37%</td>
	<td data-value="834" class="abs medium">637/834</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-11T19:48:10.828Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    